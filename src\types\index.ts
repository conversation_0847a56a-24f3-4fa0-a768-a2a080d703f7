// 基础数据类型定义
export interface Point3D {
  x: number
  y: number
  z: number
}

export interface Point2D {
  x: number
  y: number
}

// 地形数据类型
export interface TerrainData {
  id: string
  name: string
  type: 'DEM' | 'TIN' | 'CONTOUR'
  data: any
  bounds: {
    minX: number
    maxX: number
    minY: number
    maxY: number
    minZ: number
    maxZ: number
  }
  createdAt: Date
  updatedAt: Date
}

// 钻孔数据类型
export interface DrillHoleData {
  id: string
  name: string
  location: Point3D
  depth: number
  diameter: number
  layers: DrillLayer[]
  createdAt: Date
  updatedAt: Date
}

export interface DrillLayer {
  id: string
  depth: number
  thickness: number
  rockType: string
  hardness: number
  color: string
}

// 地质数据类型
export interface GeologyData {
  id: string
  name: string
  type: 'FAULT' | 'STRATUM' | 'ORE_BODY'
  geometry: any
  properties: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

// 道路设计数据类型
export interface RoadDesign {
  id: string
  name: string
  type: 'MAIN_ROAD' | 'BRANCH_ROAD' | 'RAMP'
  centerLine: Point3D[]
  width: number
  grade: number
  radius: number
  designSpeed: number
  surfaceType: string
  status: 'DRAFT' | 'APPROVED' | 'CONSTRUCTED'
  createdAt: Date
  updatedAt: Date
}

// 道路标准参数
export interface RoadStandard {
  designSpeed: number // 设计速度 km/h
  laneWidth: number // 车道宽度 m
  shoulderWidth: number // 路肩宽度 m
  maxGrade: number // 最大纵坡 %
  minRadius: number // 最小平曲线半径 m
  minVerticalRadius: number // 最小竖曲线半径 m
  sightDistance: number // 停车视距 m
}

// 运输路线数据
export interface TransportRoute {
  id: string
  name: string
  startPoint: Point3D
  endPoint: Point3D
  waypoints: Point3D[]
  distance: number
  travelTime: number
  capacity: number
  vehicleType: string
  priority: number
}

// 安全检测结果
export interface SafetyCheck {
  id: string
  roadId: string
  checkType: 'SLOPE_STABILITY' | 'SIGHT_DISTANCE' | 'GRADE_CHECK' | 'RADIUS_CHECK'
  status: 'PASS' | 'WARNING' | 'FAIL'
  message: string
  location: Point3D
  severity: 'LOW' | 'MEDIUM' | 'HIGH'
  createdAt: Date
}

// AutoCAD数据类型
export interface CADData {
  id: string
  name: string
  type: 'DWG' | 'DXF'
  layers: CADLayer[]
  bounds: {
    minX: number
    maxX: number
    minY: number
    maxY: number
  }
  createdAt: Date
  updatedAt: Date
}

export interface CADLayer {
  name: string
  color: string
  lineType: string
  entities: CADEntity[]
  visible: boolean
}

export interface CADEntity {
  id: string
  type: 'LINE' | 'POLYLINE' | 'CIRCLE' | 'ARC' | 'TEXT' | 'BLOCK'
  geometry: any
  properties: Record<string, any>
}

// 用户界面状态
export interface UIState {
  activeModule: string
  sidebarCollapsed: boolean
  workspaceMode: '2D' | '3D'
  selectedEntities: string[]
  viewerSettings: {
    showTerrain: boolean
    showDrillHoles: boolean
    showGeology: boolean
    showRoads: boolean
    showCAD: boolean
  }
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
}

// 文件上传类型
export interface FileUpload {
  file: File
  type: 'TERRAIN' | 'DRILL_HOLE' | 'GEOLOGY' | 'CAD'
  progress: number
  status: 'PENDING' | 'UPLOADING' | 'SUCCESS' | 'ERROR'
  error?: string
}
