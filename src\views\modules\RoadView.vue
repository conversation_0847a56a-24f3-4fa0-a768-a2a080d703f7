<template>
  <div class="road-view">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新建道路
      </el-button>
      <el-button @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 道路设计标准 -->
    <div class="standards-panel">
      <h4>设计标准</h4>
      <div class="standards-grid">
        <div class="standard-item">
          <span class="label">设计速度:</span>
          <span class="value">{{ roadStandards.designSpeed }} km/h</span>
        </div>
        <div class="standard-item">
          <span class="label">车道宽度:</span>
          <span class="value">{{ roadStandards.laneWidth }} m</span>
        </div>
        <div class="standard-item">
          <span class="label">最大坡度:</span>
          <span class="value">{{ roadStandards.maxGrade }}%</span>
        </div>
        <div class="standard-item">
          <span class="label">最小半径:</span>
          <span class="value">{{ roadStandards.minRadius }} m</span>
        </div>
      </div>
    </div>

    <!-- 道路列表 -->
    <div class="data-list">
      <el-scrollbar>
        <div v-if="loading" class="loading-container">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <p>加载中...</p>
        </div>
        
        <div v-else-if="roadList.length === 0" class="empty-container">
          <el-icon class="empty-icon"><Connection /></el-icon>
          <p>暂无道路设计</p>
          <el-button type="primary" @click="showCreateDialog = true">
            新建道路
          </el-button>
        </div>
        
        <div v-else class="road-items">
          <div 
            v-for="road in roadList" 
            :key="road.id"
            class="road-item"
            :class="{ active: currentRoad?.id === road.id }"
            @click="selectRoad(road)"
          >
            <div class="item-header">
              <el-icon class="item-icon"><Connection /></el-icon>
              <span class="item-name">{{ road.name }}</span>
              <el-tag 
                :type="getStatusType(road.status)" 
                size="small"
              >
                {{ getStatusText(road.status) }}
              </el-tag>
              <el-dropdown @command="handleCommand">
                <el-button text>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ action: 'edit', road }">
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'profile', road }">
                      生成剖面
                    </el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'safety', road }">
                      安全检测
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="{ action: 'delete', road }"
                      divided
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
            <div class="item-info">
              <div class="info-row">
                <span class="label">类型:</span>
                <span class="value">{{ getRoadTypeText(road.type) }}</span>
              </div>
              <div class="info-row">
                <span class="label">宽度:</span>
                <span class="value">{{ road.width }}m</span>
              </div>
              <div class="info-row">
                <span class="label">坡度:</span>
                <span class="value">{{ road.grade }}%</span>
              </div>
              <div class="info-row">
                <span class="label">设计速度:</span>
                <span class="value">{{ road.designSpeed }}km/h</span>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 创建道路对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建道路"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="道路名称">
          <el-input v-model="createForm.name" placeholder="请输入道路名称" />
        </el-form-item>
        <el-form-item label="道路类型">
          <el-select v-model="createForm.type" placeholder="请选择道路类型">
            <el-option label="主干道" value="MAIN_ROAD" />
            <el-option label="支路" value="BRANCH_ROAD" />
            <el-option label="坡道" value="RAMP" />
          </el-select>
        </el-form-item>
        <el-form-item label="道路宽度">
          <el-input-number 
            v-model="createForm.width" 
            :min="3" 
            :max="20" 
            :step="0.5"
            controls-position="right"
          />
          <span style="margin-left: 8px; color: #95a5a6;">米</span>
        </el-form-item>
        <el-form-item label="设计速度">
          <el-input-number 
            v-model="createForm.designSpeed" 
            :min="20" 
            :max="80" 
            :step="5"
            controls-position="right"
          />
          <span style="margin-left: 8px; color: #95a5a6;">km/h</span>
        </el-form-item>
        <el-form-item label="路面类型">
          <el-select v-model="createForm.surfaceType" placeholder="请选择路面类型">
            <el-option label="沥青混凝土" value="ASPHALT" />
            <el-option label="水泥混凝土" value="CONCRETE" />
            <el-option label="碎石路面" value="GRAVEL" />
            <el-option label="土路" value="DIRT" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleCreate"
          :loading="creating"
        >
          {{ creating ? '创建中...' : '确定创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoadStore } from '@/stores'
import type { RoadDesign } from '@/types'
import {
  Plus,
  Refresh,
  Loading,
  Connection,
  MoreFilled
} from '@element-plus/icons-vue'

const roadStore = useRoadStore()

const showCreateDialog = ref(false)
const creating = ref(false)

const createForm = ref({
  name: '',
  type: '',
  width: 7,
  designSpeed: 40,
  surfaceType: ''
})

const loading = computed(() => roadStore.loading)
const roadList = computed(() => roadStore.sortedRoadList)
const currentRoad = computed(() => roadStore.currentRoad)
const roadStandards = computed(() => roadStore.roadStandards)

onMounted(() => {
  refreshData()
})

const refreshData = async () => {
  try {
    await roadStore.fetchRoadList()
  } catch (error) {
    ElMessage.error('获取道路数据失败')
  }
}

const selectRoad = (road: RoadDesign) => {
  roadStore.setCurrentRoad(road)
  // 在3D视图中显示道路
  // TODO: 集成Cesium显示逻辑
}

const handleCreate = async () => {
  if (!createForm.value.name || !createForm.value.type || !createForm.value.surfaceType) {
    ElMessage.warning('请填写完整信息')
    return
  }

  creating.value = true
  
  try {
    const roadData = {
      ...createForm.value,
      centerLine: [], // 空的中心线，后续通过选线功能添加
      grade: 0,
      radius: roadStandards.value.minRadius,
      status: 'DRAFT' as const
    }
    
    await roadStore.createRoad(roadData)
    ElMessage.success('道路创建成功')
    showCreateDialog.value = false
    resetCreateForm()
  } catch (error: any) {
    ElMessage.error(error.message || '创建失败')
  } finally {
    creating.value = false
  }
}

const handleCommand = async (command: any) => {
  const { action, road } = command
  
  switch (action) {
    case 'edit':
      ElMessage.info('编辑功能开发中')
      break
      
    case 'profile':
      try {
        await roadStore.generateRoadProfile(road.id, [0, 100, 200, 300])
        ElMessage.success('剖面生成成功')
      } catch (error: any) {
        ElMessage.error(error.message || '剖面生成失败')
      }
      break
      
    case 'safety':
      try {
        await roadStore.performSafetyCheck(road.id)
        ElMessage.success('安全检测完成')
      } catch (error: any) {
        ElMessage.error(error.message || '安全检测失败')
      }
      break
      
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确定要删除道路 "${road.name}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await roadStore.deleteRoad(road.id)
        ElMessage.success('删除成功')
      } catch (error: any) {
        if (error !== 'cancel') {
          ElMessage.error(error.message || '删除失败')
        }
      }
      break
  }
}

const resetCreateForm = () => {
  createForm.value = {
    name: '',
    type: '',
    width: 7,
    designSpeed: 40,
    surfaceType: ''
  }
}

const getStatusType = (status: string) => {
  const typeMap = {
    DRAFT: 'info',
    APPROVED: 'success',
    CONSTRUCTED: 'warning'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    DRAFT: '草图',
    APPROVED: '已批准',
    CONSTRUCTED: '已建设'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getRoadTypeText = (type: string) => {
  const textMap = {
    MAIN_ROAD: '主干道',
    BRANCH_ROAD: '支路',
    RAMP: '坡道'
  }
  return textMap[type as keyof typeof textMap] || type
}
</script>

<style scoped>
.road-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 16px;
  border-bottom: 1px solid #34495e;
  display: flex;
  gap: 8px;
}

.standards-panel {
  padding: 16px;
  border-bottom: 1px solid #34495e;
  background: rgba(52, 73, 94, 0.2);
}

.standards-panel h4 {
  margin: 0 0 12px 0;
  color: #f1c40f;
  font-size: 14px;
}

.standards-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.standard-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.standard-item .label {
  color: #95a5a6;
}

.standard-item .value {
  color: #ecf0f1;
  font-weight: 600;
}

.data-list {
  flex: 1;
  overflow: hidden;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #95a5a6;
}

.loading-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.loading-icon {
  animation: rotating 2s linear infinite;
}

.road-items {
  padding: 16px;
}

.road-item {
  background: rgba(52, 73, 94, 0.3);
  border: 1px solid #34495e;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.road-item:hover {
  background: rgba(52, 73, 94, 0.5);
  border-color: #5a6c7d;
}

.road-item.active {
  background: rgba(241, 196, 15, 0.1);
  border-color: #f1c40f;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.item-icon {
  color: #f1c40f;
  font-size: 18px;
}

.item-name {
  flex: 1;
  color: #ecf0f1;
  font-weight: 600;
  font-size: 14px;
}

.item-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.info-row {
  display: flex;
  font-size: 12px;
}

.info-row .label {
  color: #95a5a6;
  width: 60px;
  flex-shrink: 0;
}

.info-row .value {
  color: #bdc3c7;
  flex: 1;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
