<template>
  <div class="app-header">
    <div class="header-left">
      <div class="logo">
        <el-icon class="logo-icon"><Setting /></el-icon>
        <span class="logo-text">露天矿山道路设计系统</span>
      </div>
    </div>
    
    <div class="header-center">
      <el-menu
        :default-active="activeModule"
        mode="horizontal"
        class="header-menu"
        @select="handleMenuSelect"
      >
        <el-menu-item index="terrain">
          <el-icon><MapLocation /></el-icon>
          <span>地形数据</span>
        </el-menu-item>
        <el-menu-item index="drillhole">
          <el-icon><Position /></el-icon>
          <span>钻孔数据</span>
        </el-menu-item>
        <el-menu-item index="geology">
          <el-icon><Histogram /></el-icon>
          <span>地质数据</span>
        </el-menu-item>
        <el-menu-item index="road">
          <el-icon><Connection /></el-icon>
          <span>道路设计</span>
        </el-menu-item>
        <el-menu-item index="transport">
          <el-icon><Truck /></el-icon>
          <span>运输路线</span>
        </el-menu-item>
        <el-menu-item index="safety">
          <el-icon><Warning /></el-icon>
          <span>安全检测</span>
        </el-menu-item>
        <el-menu-item index="cad">
          <el-icon><Document /></el-icon>
          <span>CAD数据</span>
        </el-menu-item>
      </el-menu>
    </div>
    
    <div class="header-right">
      <el-button-group>
        <el-button 
          :type="workspaceMode === '2D' ? 'primary' : 'default'"
          @click="setWorkspaceMode('2D')"
        >
          2D
        </el-button>
        <el-button 
          :type="workspaceMode === '3D' ? 'primary' : 'default'"
          @click="setWorkspaceMode('3D')"
        >
          3D
        </el-button>
      </el-button-group>
      
      <el-button 
        :icon="sidebarCollapsed ? 'Expand' : 'Fold'"
        @click="toggleSidebar"
        class="sidebar-toggle"
      />
      
      <el-dropdown>
        <el-button circle>
          <el-icon><User /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>用户设置</el-dropdown-item>
            <el-dropdown-item>帮助文档</el-dropdown-item>
            <el-dropdown-item divided>退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUIStore } from '@/stores'
import {
  Setting,
  MapLocation,
  Position,
  Histogram,
  Connection,
  Truck,
  Warning,
  Document,
  User,
  Expand,
  Fold
} from '@element-plus/icons-vue'

const router = useRouter()
const uiStore = useUIStore()

const activeModule = computed(() => uiStore.activeModule)
const workspaceMode = computed(() => uiStore.workspaceMode)
const sidebarCollapsed = computed(() => uiStore.sidebarCollapsed)

const handleMenuSelect = (index: string) => {
  uiStore.setActiveModule(index)
  router.push(`/workspace/${index}`)
}

const setWorkspaceMode = (mode: '2D' | '3D') => {
  uiStore.setWorkspaceMode(mode)
}

const toggleSidebar = () => {
  uiStore.toggleSidebar()
}
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-bottom: 1px solid #34495e;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  color: #f1c40f;
  font-weight: bold;
  font-size: 18px;
}

.logo-icon {
  margin-right: 8px;
  font-size: 24px;
}

.logo-text {
  white-space: nowrap;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 20px;
}

.header-menu {
  background: transparent;
  border: none;
}

.header-menu .el-menu-item {
  color: #ecf0f1;
  border-bottom: 2px solid transparent;
}

.header-menu .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f1c40f;
}

.header-menu .el-menu-item.is-active {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
  border-bottom-color: #f1c40f;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-toggle {
  color: #ecf0f1;
}

:deep(.el-button-group .el-button) {
  border-color: #5a6c7d;
  color: #ecf0f1;
}

:deep(.el-button-group .el-button:hover) {
  border-color: #f1c40f;
  color: #f1c40f;
}

:deep(.el-button-group .el-button.el-button--primary) {
  background-color: #f1c40f;
  border-color: #f1c40f;
  color: #2c3e50;
}
</style>
