import type { Point3D, Point2D } from '@/types'

// 数学工具函数
export const MathUtils = {
  // 计算两点间距离
  distance2D(p1: Point2D, p2: Point2D): number {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2))
  },

  distance3D(p1: Point3D, p2: Point3D): number {
    return Math.sqrt(
      Math.pow(p2.x - p1.x, 2) + 
      Math.pow(p2.y - p1.y, 2) + 
      Math.pow(p2.z - p1.z, 2)
    )
  },

  // 计算坡度
  calculateGrade(p1: Point3D, p2: Point3D): number {
    const horizontalDistance = this.distance2D(p1, p2)
    const verticalDistance = Math.abs(p2.z - p1.z)
    return horizontalDistance > 0 ? (verticalDistance / horizontalDistance) * 100 : 0
  },

  // 角度转弧度
  degToRad(degrees: number): number {
    return degrees * (Math.PI / 180)
  },

  // 弧度转角度
  radToDeg(radians: number): number {
    return radians * (180 / Math.PI)
  },

  // 线性插值
  lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t
  },

  // 点在线段上的投影
  projectPointOnLine(point: Point2D, lineStart: Point2D, lineEnd: Point2D): Point2D {
    const A = point.x - lineStart.x
    const B = point.y - lineStart.y
    const C = lineEnd.x - lineStart.x
    const D = lineEnd.y - lineStart.y

    const dot = A * C + B * D
    const lenSq = C * C + D * D
    let param = -1
    if (lenSq !== 0) param = dot / lenSq

    let xx, yy

    if (param < 0) {
      xx = lineStart.x
      yy = lineStart.y
    } else if (param > 1) {
      xx = lineEnd.x
      yy = lineEnd.y
    } else {
      xx = lineStart.x + param * C
      yy = lineStart.y + param * D
    }

    return { x: xx, y: yy }
  }
}

// 道路设计工具函数
export const RoadUtils = {
  // 检查道路坡度是否符合标准
  checkGrade(grade: number, maxGrade: number): boolean {
    return Math.abs(grade) <= maxGrade
  },

  // 检查平曲线半径是否符合标准
  checkRadius(radius: number, minRadius: number): boolean {
    return radius >= minRadius
  },

  // 计算停车视距
  calculateSightDistance(speed: number): number {
    // 简化公式：停车视距 = 0.278 * v * t + v² / (254 * f)
    // v: 速度(km/h), t: 反应时间(s), f: 摩擦系数
    const reactionTime = 2.5 // 反应时间2.5秒
    const frictionCoeff = 0.35 // 摩擦系数
    return 0.278 * speed * reactionTime + Math.pow(speed, 2) / (254 * frictionCoeff)
  },

  // 生成道路中心线
  generateCenterLine(points: Point3D[], smoothing: boolean = true): Point3D[] {
    if (points.length < 2) return points
    
    if (!smoothing) return points

    // 简单的样条插值
    const result: Point3D[] = []
    for (let i = 0; i < points.length - 1; i++) {
      result.push(points[i])
      
      // 在两点间插入中间点
      const midPoint: Point3D = {
        x: (points[i].x + points[i + 1].x) / 2,
        y: (points[i].y + points[i + 1].y) / 2,
        z: (points[i].z + points[i + 1].z) / 2
      }
      result.push(midPoint)
    }
    result.push(points[points.length - 1])
    
    return result
  }
}

// 文件处理工具函数
export const FileUtils = {
  // 读取文件为文本
  readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = (e) => reject(e)
      reader.readAsText(file)
    })
  },

  // 读取文件为ArrayBuffer
  readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as ArrayBuffer)
      reader.onerror = (e) => reject(e)
      reader.readAsArrayBuffer(file)
    })
  },

  // 下载文件
  downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  },

  // 获取文件扩展名
  getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || ''
  },

  // 格式化文件大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// 颜色工具函数
export const ColorUtils = {
  // 十六进制转RGB
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  },

  // RGB转十六进制
  rgbToHex(r: number, g: number, b: number): string {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
  },

  // 生成随机颜色
  randomColor(): string {
    return '#' + Math.floor(Math.random() * 16777215).toString(16)
  }
}

// 日期工具函数
export const DateUtils = {
  // 格式化日期
  formatDate(date: Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  },

  // 获取相对时间
  getRelativeTime(date: Date): string {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (days > 0) return `${days}天前`
    if (hours > 0) return `${hours}小时前`
    if (minutes > 0) return `${minutes}分钟前`
    return '刚刚'
  }
}

// 验证工具函数
export const ValidationUtils = {
  // 验证邮箱
  isEmail(email: string): boolean {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  },

  // 验证数字
  isNumber(value: any): boolean {
    return !isNaN(value) && !isNaN(parseFloat(value))
  },

  // 验证坐标
  isValidCoordinate(coord: Point3D): boolean {
    return this.isNumber(coord.x) && this.isNumber(coord.y) && this.isNumber(coord.z)
  }
}
