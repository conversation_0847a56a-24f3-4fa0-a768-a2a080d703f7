import { defineStore } from 'pinia'
import type { UIState } from '@/types'

export const useUIStore = defineStore('ui', {
  state: (): UIState => ({
    activeModule: 'terrain',
    sidebarCollapsed: false,
    workspaceMode: '3D',
    selectedEntities: [],
    viewerSettings: {
      showTerrain: true,
      showDrillHoles: true,
      showGeology: true,
      showRoads: true,
      showCAD: true
    }
  }),

  getters: {
    isModuleActive: (state) => (module: string) => state.activeModule === module,
    is3DMode: (state) => state.workspaceMode === '3D',
    hasSelectedEntities: (state) => state.selectedEntities.length > 0,
    selectedEntityCount: (state) => state.selectedEntities.length
  },

  actions: {
    setActiveModule(module: string) {
      this.activeModule = module
    },

    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    setSidebarCollapsed(collapsed: boolean) {
      this.sidebarCollapsed = collapsed
    },

    setWorkspaceMode(mode: '2D' | '3D') {
      this.workspaceMode = mode
    },

    toggleWorkspaceMode() {
      this.workspaceMode = this.workspaceMode === '2D' ? '3D' : '2D'
    },

    selectEntity(entityId: string) {
      if (!this.selectedEntities.includes(entityId)) {
        this.selectedEntities.push(entityId)
      }
    },

    deselectEntity(entityId: string) {
      const index = this.selectedEntities.indexOf(entityId)
      if (index > -1) {
        this.selectedEntities.splice(index, 1)
      }
    },

    toggleEntitySelection(entityId: string) {
      if (this.selectedEntities.includes(entityId)) {
        this.deselectEntity(entityId)
      } else {
        this.selectEntity(entityId)
      }
    },

    clearSelection() {
      this.selectedEntities = []
    },

    setMultipleSelection(entityIds: string[]) {
      this.selectedEntities = [...entityIds]
    },

    updateViewerSetting(key: keyof UIState['viewerSettings'], value: boolean) {
      this.viewerSettings[key] = value
    },

    toggleViewerSetting(key: keyof UIState['viewerSettings']) {
      this.viewerSettings[key] = !this.viewerSettings[key]
    },

    resetViewerSettings() {
      this.viewerSettings = {
        showTerrain: true,
        showDrillHoles: true,
        showGeology: true,
        showRoads: true,
        showCAD: true
      }
    }
  },

  persist: {
    key: 'openpit-ui-state',
    storage: localStorage,
    paths: ['sidebarCollapsed', 'workspaceMode', 'viewerSettings']
  }
})
