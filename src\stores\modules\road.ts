import { defineStore } from 'pinia'
import type { RoadDesign, RoadStandard, SafetyCheck } from '@/types'
import { roadAPI } from '@/services/api'

interface RoadState {
  roadList: RoadDesign[]
  currentRoad: RoadDesign | null
  roadStandards: RoadStandard
  safetyChecks: SafetyCheck[]
  loading: boolean
  error: string | null
}

export const useRoadStore = defineStore('road', {
  state: (): RoadState => ({
    roadList: [],
    currentRoad: null,
    roadStandards: {
      designSpeed: 40, // km/h
      laneWidth: 3.5, // m
      shoulderWidth: 0.5, // m
      maxGrade: 8, // %
      minRadius: 30, // m
      minVerticalRadius: 200, // m
      sightDistance: 40 // m
    },
    safetyChecks: [],
    loading: false,
    error: null
  }),

  getters: {
    hasRoadData: (state) => state.roadList.length > 0,
    roadCount: (state) => state.roadList.length,
    getRoadById: (state) => (id: string) => 
      state.roadList.find(road => road.id === id),
    sortedRoadList: (state) => 
      [...state.roadList].sort((a, b) => 
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      ),
    mainRoads: (state) => state.roadList.filter(road => road.type === 'MAIN_ROAD'),
    branchRoads: (state) => state.roadList.filter(road => road.type === 'BRANCH_ROAD'),
    ramps: (state) => state.roadList.filter(road => road.type === 'RAMP'),
    approvedRoads: (state) => state.roadList.filter(road => road.status === 'APPROVED'),
    draftRoads: (state) => state.roadList.filter(road => road.status === 'DRAFT'),
    constructedRoads: (state) => state.roadList.filter(road => road.status === 'CONSTRUCTED'),
    failedSafetyChecks: (state) => state.safetyChecks.filter(check => check.status === 'FAIL'),
    warningSafetyChecks: (state) => state.safetyChecks.filter(check => check.status === 'WARNING')
  },

  actions: {
    async fetchRoadList() {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.getRoadList()
        if (response.success) {
          this.roadList = response.data
        } else {
          this.error = response.message
        }
      } catch (error: any) {
        this.error = error.message || '获取道路数据失败'
      } finally {
        this.loading = false
      }
    },

    async fetchRoadById(id: string) {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.getRoadById(id)
        if (response.success) {
          this.currentRoad = response.data
          
          // 更新列表中的数据
          const index = this.roadList.findIndex(road => road.id === id)
          if (index > -1) {
            this.roadList[index] = response.data
          }
        } else {
          this.error = response.message
        }
      } catch (error: any) {
        this.error = error.message || '获取道路数据详情失败'
      } finally {
        this.loading = false
      }
    },

    async createRoad(roadData: Partial<RoadDesign>) {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.createRoad(roadData)
        if (response.success) {
          this.roadList.push(response.data)
          return response.data
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '创建道路失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async updateRoad(id: string, roadData: Partial<RoadDesign>) {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.updateRoad(id, roadData)
        if (response.success) {
          const index = this.roadList.findIndex(road => road.id === id)
          if (index > -1) {
            this.roadList[index] = response.data
          }
          
          if (this.currentRoad?.id === id) {
            this.currentRoad = response.data
          }
          
          return response.data
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '更新道路失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async deleteRoad(id: string) {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.deleteRoad(id)
        if (response.success) {
          const index = this.roadList.findIndex(road => road.id === id)
          if (index > -1) {
            this.roadList.splice(index, 1)
          }
          
          if (this.currentRoad?.id === id) {
            this.currentRoad = null
          }
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '删除道路失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async generateRoadAlignment(startPoint: any, endPoint: any, constraints: any) {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.generateRoadAlignment(startPoint, endPoint, constraints)
        if (response.success) {
          return response.data
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '道路选线失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async checkRoadConflicts(roadId: string) {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.checkRoadConflicts(roadId)
        if (response.success) {
          return response.data
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '道路冲突检测失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async generateRoadProfile(roadId: string, stations: number[]) {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.generateRoadProfile(roadId, stations)
        if (response.success) {
          return response.data
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '道路剖切失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async performSafetyCheck(roadId: string) {
      this.loading = true
      this.error = null
      
      try {
        const response = await roadAPI.performSafetyCheck(roadId)
        if (response.success) {
          this.safetyChecks = response.data
          return response.data
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '安全检测失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    setCurrentRoad(road: RoadDesign | null) {
      this.currentRoad = road
    },

    updateRoadStandards(standards: Partial<RoadStandard>) {
      this.roadStandards = { ...this.roadStandards, ...standards }
    },

    addSafetyCheck(check: SafetyCheck) {
      this.safetyChecks.push(check)
    },

    clearSafetyChecks() {
      this.safetyChecks = []
    },

    clearError() {
      this.error = null
    },

    reset() {
      this.roadList = []
      this.currentRoad = null
      this.safetyChecks = []
      this.loading = false
      this.error = null
    }
  }
})
