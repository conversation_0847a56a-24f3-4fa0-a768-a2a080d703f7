import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/workspace'
    },
    {
      path: '/workspace',
      name: 'workspace',
      component: () => import('../views/WorkspaceView.vue'),
      children: [
        {
          path: '',
          redirect: '/workspace/terrain'
        },
        {
          path: 'terrain',
          name: 'terrain',
          component: () => import('../views/modules/TerrainView.vue')
        },
        {
          path: 'drillhole',
          name: 'drillhole',
          component: () => import('../views/modules/DrillHoleView.vue')
        },
        {
          path: 'geology',
          name: 'geology',
          component: () => import('../views/modules/GeologyView.vue')
        },
        {
          path: 'road',
          name: 'road',
          component: () => import('../views/modules/RoadView.vue')
        },
        {
          path: 'transport',
          name: 'transport',
          component: () => import('../views/modules/TransportView.vue')
        },
        {
          path: 'safety',
          name: 'safety',
          component: () => import('../views/modules/SafetyView.vue')
        },
        {
          path: 'cad',
          name: 'cad',
          component: () => import('../views/modules/CADView.vue')
        }
      ]
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue')
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'notFound',
      component: () => import('../views/NotFoundView.vue')
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 这里可以添加认证逻辑
  next()
})

export default router
