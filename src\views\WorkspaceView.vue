<template>
  <div class="workspace-layout">
    <!-- 顶部导航栏 -->
    <AppHeader />
    
    <!-- 主要内容区域 -->
    <div class="workspace-main">
      <!-- 左侧边栏 -->
      <AppSidebar>
        <router-view name="sidebar" />
        <router-view />
      </AppSidebar>
      
      <!-- 中间工作区 -->
      <div class="workspace-content">
        <!-- 3D/2D 视图区域 -->
        <div class="viewer-container">
          <CesiumViewer 
            v-if="workspaceMode === '3D'"
            ref="cesiumViewer"
            @entity-selected="handleEntitySelected"
            @entity-deselected="handleEntityDeselected"
          />
          <div v-else class="2d-viewer">
            <div class="viewer-placeholder">
              <el-icon class="placeholder-icon"><MapLocation /></el-icon>
              <p>2D视图开发中...</p>
            </div>
          </div>
        </div>
        
        <!-- 状态栏 -->
        <div class="status-bar">
          <div class="status-left">
            <span class="status-item">
              <el-icon><Location /></el-icon>
              坐标: {{ currentCoordinates }}
            </span>
            <span class="status-item">
              <el-icon><View /></el-icon>
              视图: {{ workspaceMode }}
            </span>
          </div>
          
          <div class="status-right">
            <span class="status-item">
              <el-icon><Select /></el-icon>
              已选择: {{ selectedEntityCount }} 项
            </span>
            <span class="status-item" v-if="loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              加载中...
            </span>
          </div>
        </div>
      </div>
      
      <!-- 右侧浮动按钮 -->
      <WorkspaceFloatingButtons
        @reset-view="handleResetView"
        @fit-view="handleFitView"
        @set-top-view="handleSetTopView"
        @set-front-view="handleSetFrontView"
        @tool-changed="handleToolChanged"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUIStore } from '@/stores'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppSidebar from '@/components/layout/AppSidebar.vue'
import WorkspaceFloatingButtons from '@/components/layout/WorkspaceFloatingButtons.vue'
import CesiumViewer from '@/components/cesium/CesiumViewer.vue'
import {
  MapLocation,
  Location,
  View,
  Select,
  Loading
} from '@element-plus/icons-vue'

const uiStore = useUIStore()

const cesiumViewer = ref()
const currentCoordinates = ref('0.0, 0.0, 0.0')
const loading = ref(false)

const workspaceMode = computed(() => uiStore.workspaceMode)
const selectedEntityCount = computed(() => uiStore.selectedEntityCount)

onMounted(() => {
  // 初始化工作区
  initWorkspace()
})

const initWorkspace = () => {
  // 初始化逻辑
  console.log('工作区初始化完成')
}

const handleEntitySelected = (entityId: string) => {
  uiStore.selectEntity(entityId)
}

const handleEntityDeselected = (entityId: string) => {
  uiStore.deselectEntity(entityId)
}

const handleResetView = () => {
  if (cesiumViewer.value) {
    cesiumViewer.value.resetView()
  }
}

const handleFitView = () => {
  if (cesiumViewer.value) {
    cesiumViewer.value.fitView()
  }
}

const handleSetTopView = () => {
  if (cesiumViewer.value) {
    cesiumViewer.value.setTopView()
  }
}

const handleSetFrontView = () => {
  if (cesiumViewer.value) {
    cesiumViewer.value.setFrontView()
  }
}

const handleToolChanged = (tool: string | null) => {
  if (cesiumViewer.value) {
    cesiumViewer.value.setActiveTool(tool)
  }
}

// 监听鼠标位置更新坐标显示
const updateCoordinates = (coordinates: string) => {
  currentCoordinates.value = coordinates
}

defineExpose({
  updateCoordinates
})
</script>

<style scoped>
.workspace-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #2c3e50;
}

.workspace-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.workspace-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.viewer-container {
  flex: 1;
  position: relative;
  background: #34495e;
}

.2d-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
}

.viewer-placeholder {
  text-align: center;
  color: #95a5a6;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #7f8c8d;
}

.viewer-placeholder p {
  font-size: 18px;
  margin: 0;
}

.status-bar {
  height: 32px;
  background: #2c3e50;
  border-top: 1px solid #34495e;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: 12px;
  color: #95a5a6;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-item .el-icon {
  font-size: 14px;
}

.status-item .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .workspace-main {
    flex-direction: column;
  }
  
  .status-bar {
    font-size: 11px;
  }
  
  .status-left,
  .status-right {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .status-left .status-item:not(:first-child),
  .status-right .status-item:not(:first-child) {
    display: none;
  }
}
</style>
