<template>
  <div class="app-sidebar" :class="{ collapsed: sidebarCollapsed }">
    <div class="sidebar-header">
      <h3 v-if="!sidebarCollapsed">{{ moduleTitle }}</h3>
      <el-icon v-else class="module-icon">
        <component :is="moduleIcon" />
      </el-icon>
    </div>
    
    <div class="sidebar-content">
      <el-scrollbar>
        <slot />
      </el-scrollbar>
    </div>
    
    <div class="sidebar-footer" v-if="!sidebarCollapsed">
      <div class="data-summary">
        <div class="summary-item">
          <span class="label">选中项目:</span>
          <span class="value">{{ selectedEntityCount }}</span>
        </div>
        <div class="summary-item" v-if="activeModule === 'terrain'">
          <span class="label">地形数据:</span>
          <span class="value">{{ terrainCount }}</span>
        </div>
        <div class="summary-item" v-if="activeModule === 'road'">
          <span class="label">道路数量:</span>
          <span class="value">{{ roadCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUIStore, useTerrainStore, useRoadStore } from '@/stores'
import {
  MapLocation,
  Position,
  Histogram,
  Connection,
  Truck,
  Warning,
  Document
} from '@element-plus/icons-vue'

const uiStore = useUIStore()
const terrainStore = useTerrainStore()
const roadStore = useRoadStore()

const sidebarCollapsed = computed(() => uiStore.sidebarCollapsed)
const activeModule = computed(() => uiStore.activeModule)
const selectedEntityCount = computed(() => uiStore.selectedEntityCount)
const terrainCount = computed(() => terrainStore.terrainCount)
const roadCount = computed(() => roadStore.roadCount)

const moduleConfig = {
  terrain: { title: '地形数据管理', icon: 'MapLocation' },
  drillhole: { title: '钻孔数据管理', icon: 'Position' },
  geology: { title: '地质数据管理', icon: 'Histogram' },
  road: { title: '道路设计', icon: 'Connection' },
  transport: { title: '运输路线', icon: 'Truck' },
  safety: { title: '安全检测', icon: 'Warning' },
  cad: { title: 'CAD数据', icon: 'Document' }
}

const moduleTitle = computed(() => {
  return moduleConfig[activeModule.value as keyof typeof moduleConfig]?.title || '数据管理'
})

const moduleIcon = computed(() => {
  const iconName = moduleConfig[activeModule.value as keyof typeof moduleConfig]?.icon
  const iconMap = {
    MapLocation,
    Position,
    Histogram,
    Connection,
    Truck,
    Warning,
    Document
  }
  return iconMap[iconName as keyof typeof iconMap] || MapLocation
})
</script>

<style scoped>
.app-sidebar {
  width: 320px;
  height: 100%;
  background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%);
  border-right: 1px solid #34495e;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;
}

.app-sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  border-bottom: 1px solid #34495e;
  background: rgba(0, 0, 0, 0.1);
}

.sidebar-header h3 {
  color: #f1c40f;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.module-icon {
  color: #f1c40f;
  font-size: 24px;
}

.sidebar-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.collapsed .sidebar-content {
  padding: 10px;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #34495e;
  background: rgba(0, 0, 0, 0.1);
}

.data-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.summary-item .label {
  color: #95a5a6;
}

.summary-item .value {
  color: #f1c40f;
  font-weight: 600;
}

:deep(.el-scrollbar) {
  height: 100%;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

:deep(.el-scrollbar__bar.is-vertical) {
  right: 2px;
  width: 4px;
}

:deep(.el-scrollbar__thumb) {
  background: rgba(241, 196, 15, 0.3);
  border-radius: 2px;
}

:deep(.el-scrollbar__thumb:hover) {
  background: rgba(241, 196, 15, 0.5);
}
</style>
