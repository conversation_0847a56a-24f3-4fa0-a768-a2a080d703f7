<template>
  <div class="floating-buttons">
    <!-- 视图控制按钮组 -->
    <div class="button-group view-controls">
      <el-tooltip content="重置视图" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          @click="resetView"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
      </el-tooltip>
      
      <el-tooltip content="适应视图" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          @click="fitView"
        >
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </el-tooltip>
      
      <el-tooltip content="顶视图" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          @click="setTopView"
        >
          <el-icon><Top /></el-icon>
        </el-button>
      </el-tooltip>
      
      <el-tooltip content="前视图" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          @click="setFrontView"
        >
          <el-icon><Bottom /></el-icon>
        </el-button>
      </el-tooltip>
    </div>

    <!-- 测量工具按钮组 -->
    <div class="button-group measure-tools">
      <el-tooltip content="距离测量" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          :type="activeTool === 'distance' ? 'primary' : 'default'"
          @click="toggleTool('distance')"
        >
          <el-icon><Ruler /></el-icon>
        </el-button>
      </el-tooltip>
      
      <el-tooltip content="面积测量" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          :type="activeTool === 'area' ? 'primary' : 'default'"
          @click="toggleTool('area')"
        >
          <el-icon><Grid /></el-icon>
        </el-button>
      </el-tooltip>
      
      <el-tooltip content="高程测量" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          :type="activeTool === 'elevation' ? 'primary' : 'default'"
          @click="toggleTool('elevation')"
        >
          <el-icon><TrendCharts /></el-icon>
        </el-button>
      </el-tooltip>
    </div>

    <!-- 绘制工具按钮组 -->
    <div class="button-group draw-tools">
      <el-tooltip content="绘制点" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          :type="activeTool === 'point' ? 'primary' : 'default'"
          @click="toggleTool('point')"
        >
          <el-icon><Location /></el-icon>
        </el-button>
      </el-tooltip>
      
      <el-tooltip content="绘制线" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          :type="activeTool === 'line' ? 'primary' : 'default'"
          @click="toggleTool('line')"
        >
          <el-icon><Connection /></el-icon>
        </el-button>
      </el-tooltip>
      
      <el-tooltip content="绘制面" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          :type="activeTool === 'polygon' ? 'primary' : 'default'"
          @click="toggleTool('polygon')"
        >
          <el-icon><Crop /></el-icon>
        </el-button>
      </el-tooltip>
    </div>

    <!-- 图层控制按钮 -->
    <div class="button-group layer-controls">
      <el-tooltip content="图层管理" placement="left">
        <el-button 
          circle 
          size="large"
          class="floating-btn"
          @click="toggleLayerPanel"
        >
          <el-icon><Menu /></el-icon>
        </el-button>
      </el-tooltip>
    </div>

    <!-- 图层控制面板 -->
    <el-drawer
      v-model="layerPanelVisible"
      title="图层控制"
      direction="rtl"
      size="300px"
      class="layer-drawer"
    >
      <div class="layer-panel">
        <div class="layer-group">
          <h4>基础图层</h4>
          <div class="layer-item">
            <el-switch 
              v-model="viewerSettings.showTerrain"
              @change="updateViewerSetting('showTerrain', $event)"
            />
            <span>地形数据</span>
          </div>
          <div class="layer-item">
            <el-switch 
              v-model="viewerSettings.showDrillHoles"
              @change="updateViewerSetting('showDrillHoles', $event)"
            />
            <span>钻孔数据</span>
          </div>
          <div class="layer-item">
            <el-switch 
              v-model="viewerSettings.showGeology"
              @change="updateViewerSetting('showGeology', $event)"
            />
            <span>地质数据</span>
          </div>
        </div>
        
        <div class="layer-group">
          <h4>设计图层</h4>
          <div class="layer-item">
            <el-switch 
              v-model="viewerSettings.showRoads"
              @change="updateViewerSetting('showRoads', $event)"
            />
            <span>道路设计</span>
          </div>
          <div class="layer-item">
            <el-switch 
              v-model="viewerSettings.showCAD"
              @change="updateViewerSetting('showCAD', $event)"
            />
            <span>CAD数据</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUIStore } from '@/stores'
import {
  Refresh,
  FullScreen,
  Top,
  Bottom,
  Ruler,
  Grid,
  TrendCharts,
  Location,
  Connection,
  Crop,
  Menu
} from '@element-plus/icons-vue'

const uiStore = useUIStore()

const activeTool = ref<string | null>(null)
const layerPanelVisible = ref(false)

const viewerSettings = computed(() => uiStore.viewerSettings)

const emit = defineEmits([
  'reset-view',
  'fit-view',
  'set-top-view',
  'set-front-view',
  'tool-changed'
])

const resetView = () => {
  emit('reset-view')
}

const fitView = () => {
  emit('fit-view')
}

const setTopView = () => {
  emit('set-top-view')
}

const setFrontView = () => {
  emit('set-front-view')
}

const toggleTool = (tool: string) => {
  if (activeTool.value === tool) {
    activeTool.value = null
  } else {
    activeTool.value = tool
  }
  emit('tool-changed', activeTool.value)
}

const toggleLayerPanel = () => {
  layerPanelVisible.value = !layerPanelVisible.value
}

const updateViewerSetting = (key: string, value: boolean) => {
  uiStore.updateViewerSetting(key as any, value)
}
</script>

<style scoped>
.floating-buttons {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: rgba(44, 62, 80, 0.9);
  border-radius: 12px;
  border: 1px solid #34495e;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.floating-btn {
  width: 48px;
  height: 48px;
  background: rgba(52, 73, 94, 0.8);
  border: 1px solid #5a6c7d;
  color: #ecf0f1;
  transition: all 0.3s ease;
}

.floating-btn:hover {
  background: rgba(241, 196, 15, 0.2);
  border-color: #f1c40f;
  color: #f1c40f;
  transform: scale(1.05);
}

.floating-btn.el-button--primary {
  background: #f1c40f;
  border-color: #f1c40f;
  color: #2c3e50;
}

.floating-btn.el-button--primary:hover {
  background: #f39c12;
  border-color: #f39c12;
}

.layer-panel {
  padding: 20px;
}

.layer-group {
  margin-bottom: 24px;
}

.layer-group h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.layer-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #ecf0f1;
}

.layer-item:last-child {
  border-bottom: none;
}

.layer-item span {
  color: #34495e;
  font-size: 14px;
}

:deep(.layer-drawer .el-drawer__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 0;
}

:deep(.layer-drawer .el-drawer__body) {
  padding: 0;
}
</style>
