import { defineStore } from 'pinia'
import type { TerrainData } from '@/types'
import { terrainAPI } from '@/services/api'

interface TerrainState {
  terrainList: TerrainData[]
  currentTerrain: TerrainData | null
  loading: boolean
  error: string | null
}

export const useTerrainStore = defineStore('terrain', {
  state: (): TerrainState => ({
    terrainList: [],
    currentTerrain: null,
    loading: false,
    error: null
  }),

  getters: {
    hasTerrainData: (state) => state.terrainList.length > 0,
    terrainCount: (state) => state.terrainList.length,
    getTerrainById: (state) => (id: string) => 
      state.terrainList.find(terrain => terrain.id === id),
    sortedTerrainList: (state) => 
      [...state.terrainList].sort((a, b) => 
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      )
  },

  actions: {
    async fetchTerrainList() {
      this.loading = true
      this.error = null
      
      try {
        const response = await terrainAPI.getTerrainList()
        if (response.success) {
          this.terrainList = response.data
        } else {
          this.error = response.message
        }
      } catch (error: any) {
        this.error = error.message || '获取地形数据失败'
      } finally {
        this.loading = false
      }
    },

    async fetchTerrainById(id: string) {
      this.loading = true
      this.error = null
      
      try {
        const response = await terrainAPI.getTerrainById(id)
        if (response.success) {
          this.currentTerrain = response.data
          
          // 更新列表中的数据
          const index = this.terrainList.findIndex(terrain => terrain.id === id)
          if (index > -1) {
            this.terrainList[index] = response.data
          }
        } else {
          this.error = response.message
        }
      } catch (error: any) {
        this.error = error.message || '获取地形数据详情失败'
      } finally {
        this.loading = false
      }
    },

    async uploadTerrain(file: File, name: string) {
      this.loading = true
      this.error = null
      
      try {
        const response = await terrainAPI.uploadTerrain(file, name)
        if (response.success) {
          // 添加到列表
          this.terrainList.push(response.data)
          return response.data
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '上传地形数据失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async deleteTerrain(id: string) {
      this.loading = true
      this.error = null
      
      try {
        const response = await terrainAPI.deleteTerrain(id)
        if (response.success) {
          // 从列表中移除
          const index = this.terrainList.findIndex(terrain => terrain.id === id)
          if (index > -1) {
            this.terrainList.splice(index, 1)
          }
          
          // 如果是当前地形，清空
          if (this.currentTerrain?.id === id) {
            this.currentTerrain = null
          }
        } else {
          this.error = response.message
          throw new Error(response.message)
        }
      } catch (error: any) {
        this.error = error.message || '删除地形数据失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    setCurrentTerrain(terrain: TerrainData | null) {
      this.currentTerrain = terrain
    },

    addTerrain(terrain: TerrainData) {
      this.terrainList.push(terrain)
    },

    updateTerrain(terrain: TerrainData) {
      const index = this.terrainList.findIndex(t => t.id === terrain.id)
      if (index > -1) {
        this.terrainList[index] = terrain
      }
      
      if (this.currentTerrain?.id === terrain.id) {
        this.currentTerrain = terrain
      }
    },

    removeTerrain(id: string) {
      const index = this.terrainList.findIndex(terrain => terrain.id === id)
      if (index > -1) {
        this.terrainList.splice(index, 1)
      }
      
      if (this.currentTerrain?.id === id) {
        this.currentTerrain = null
      }
    },

    clearError() {
      this.error = null
    },

    reset() {
      this.terrainList = []
      this.currentTerrain = null
      this.loading = false
      this.error = null
    }
  }
})
