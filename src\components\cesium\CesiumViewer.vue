<template>
  <div class="cesium-viewer-container">
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <p>正在加载三维场景...</p>
    </div>
    
    <!-- 工具栏 -->
    <div class="cesium-toolbar">
      <el-button-group size="small">
        <el-button @click="toggleTerrain">
          <el-icon><MapLocation /></el-icon>
          {{ showTerrain ? '隐藏' : '显示' }}地形
        </el-button>
        <el-button @click="toggleImagery">
          <el-icon><Picture /></el-icon>
          {{ showImagery ? '隐藏' : '显示' }}影像
        </el-button>
      </el-button-group>
    </div>
    
    <!-- 坐标显示 -->
    <div class="coordinate-display" v-if="mousePosition">
      <span>经度: {{ mousePosition.longitude.toFixed(6) }}°</span>
      <span>纬度: {{ mousePosition.latitude.toFixed(6) }}°</span>
      <span>高程: {{ mousePosition.height.toFixed(2) }}m</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as Cesium from 'cesium'
import { Loading, MapLocation, Picture } from '@element-plus/icons-vue'

// 设置Cesium的静态资源路径
window.CESIUM_BASE_URL = '/cesium/'

const cesiumContainer = ref<HTMLElement>()
const loading = ref(true)
const showTerrain = ref(true)
const showImagery = ref(true)
const mousePosition = ref<{
  longitude: number
  latitude: number
  height: number
} | null>(null)

let viewer: Cesium.Viewer | null = null
let handler: Cesium.ScreenSpaceEventHandler | null = null

const emit = defineEmits([
  'viewer-ready',
  'entity-selected',
  'entity-deselected',
  'coordinates-changed'
])

onMounted(async () => {
  await nextTick()
  initCesiumViewer()
})

onUnmounted(() => {
  if (handler) {
    handler.destroy()
  }
  if (viewer) {
    viewer.destroy()
  }
})

const initCesiumViewer = async () => {
  try {
    if (!cesiumContainer.value) return

    // 创建Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 基础配置
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      
      // 地形配置
      terrainProvider: Cesium.createWorldTerrain({
        requestWaterMask: true,
        requestVertexNormals: true
      }),
      
      // 影像配置
      imageryProvider: new Cesium.OpenStreetMapImageryProvider({
        url: 'https://a.tile.openstreetmap.org/'
      }),
      
      // 场景配置
      skyBox: new Cesium.SkyBox({
        sources: {
          positiveX: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_px.jpg',
          negativeX: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_mx.jpg',
          positiveY: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_py.jpg',
          negativeY: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_my.jpg',
          positiveZ: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_pz.jpg',
          negativeZ: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_mz.jpg'
        }
      }),
      
      // 其他配置
      contextOptions: {
        webgl: {
          alpha: false,
          depth: true,
          stencil: false,
          antialias: true,
          powerPreference: 'high-performance',
          premultipliedAlpha: true,
          preserveDrawingBuffer: false,
          failIfMajorPerformanceCaveat: false
        }
      }
    })

    // 场景配置
    viewer.scene.globe.enableLighting = true
    viewer.scene.globe.depthTestAgainstTerrain = true
    viewer.scene.skyAtmosphere.show = true
    viewer.scene.sun.show = true
    viewer.scene.moon.show = true
    viewer.scene.skyBox.show = true

    // 相机配置
    viewer.scene.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(116.4, 39.9, 1000000), // 北京上空
      orientation: {
        heading: 0.0,
        pitch: -Cesium.Math.PI_OVER_TWO,
        roll: 0.0
      }
    })

    // 设置鼠标事件处理
    setupMouseEvents()
    
    // 设置选择事件
    setupSelectionEvents()

    loading.value = false
    emit('viewer-ready', viewer)

  } catch (error) {
    console.error('Cesium初始化失败:', error)
    loading.value = false
  }
}

const setupMouseEvents = () => {
  if (!viewer) return

  handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
  
  // 鼠标移动事件
  handler.setInputAction((event: any) => {
    const cartesian = viewer!.camera.pickEllipsoid(event.endPosition, viewer!.scene.globe.ellipsoid)
    if (cartesian) {
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
      const longitude = Cesium.Math.toDegrees(cartographic.longitude)
      const latitude = Cesium.Math.toDegrees(cartographic.latitude)
      const height = cartographic.height

      mousePosition.value = { longitude, latitude, height }
      emit('coordinates-changed', `${longitude.toFixed(6)}, ${latitude.toFixed(6)}, ${height.toFixed(2)}`)
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
}

const setupSelectionEvents = () => {
  if (!viewer) return

  // 点击选择事件
  viewer.selectedEntityChanged.addEventListener((selectedEntity: Cesium.Entity) => {
    if (selectedEntity) {
      emit('entity-selected', selectedEntity.id)
    } else {
      emit('entity-deselected', '')
    }
  })
}

const toggleTerrain = () => {
  if (!viewer) return
  
  showTerrain.value = !showTerrain.value
  viewer.scene.globe.show = showTerrain.value
}

const toggleImagery = () => {
  if (!viewer) return
  
  showImagery.value = !showImagery.value
  viewer.scene.imageryLayers.get(0).show = showImagery.value
}

// 暴露给父组件的方法
const resetView = () => {
  if (!viewer) return
  
  viewer.scene.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(116.4, 39.9, 1000000),
    orientation: {
      heading: 0.0,
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0.0
    }
  })
}

const fitView = () => {
  if (!viewer) return
  
  const entities = viewer.entities.values
  if (entities.length > 0) {
    viewer.zoomTo(viewer.entities)
  }
}

const setTopView = () => {
  if (!viewer) return
  
  const center = viewer.scene.camera.positionCartographic
  viewer.scene.camera.setView({
    destination: Cesium.Cartesian3.fromRadians(center.longitude, center.latitude, 10000),
    orientation: {
      heading: 0.0,
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0.0
    }
  })
}

const setFrontView = () => {
  if (!viewer) return
  
  const center = viewer.scene.camera.positionCartographic
  viewer.scene.camera.setView({
    destination: Cesium.Cartesian3.fromRadians(center.longitude, center.latitude, 5000),
    orientation: {
      heading: 0.0,
      pitch: 0.0,
      roll: 0.0
    }
  })
}

const setActiveTool = (tool: string | null) => {
  // 工具切换逻辑
  console.log('切换工具:', tool)
}

const addEntity = (entity: any) => {
  if (!viewer) return null
  return viewer.entities.add(entity)
}

const removeEntity = (entity: any) => {
  if (!viewer) return
  viewer.entities.remove(entity)
}

const getViewer = () => viewer

defineExpose({
  resetView,
  fitView,
  setTopView,
  setFrontView,
  setActiveTool,
  addEntity,
  removeEntity,
  getViewer
})
</script>

<style scoped>
.cesium-viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ecf0f1;
  z-index: 1000;
}

.loading-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: rotating 2s linear infinite;
}

.loading-overlay p {
  font-size: 16px;
  margin: 0;
}

.cesium-toolbar {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 100;
}

.coordinate-display {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(44, 62, 80, 0.9);
  color: #ecf0f1;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  gap: 16px;
  z-index: 100;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Cesium样式覆盖 */
:deep(.cesium-viewer-bottom) {
  display: none;
}

:deep(.cesium-widget-credits) {
  display: none;
}

:deep(.cesium-viewer) {
  font-family: inherit;
}
</style>
