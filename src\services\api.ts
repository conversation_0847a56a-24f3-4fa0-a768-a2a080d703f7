import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 基础API类
class BaseAPI {
  protected api = api

  protected async request<T = any>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.api.request<ApiResponse<T>>(config)
      return response.data
    } catch (error: any) {
      throw {
        success: false,
        message: error.response?.data?.message || error.message || '请求失败',
        code: error.response?.status || 500,
        data: null
      }
    }
  }

  protected get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'GET', url })
  }

  protected post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'POST', url, data })
  }

  protected put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT', url, data })
  }

  protected delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE', url })
  }
}

// 地形数据API
export class TerrainAPI extends BaseAPI {
  // 获取地形数据列表
  async getTerrainList() {
    return this.get('/terrain')
  }

  // 获取地形数据详情
  async getTerrainById(id: string) {
    return this.get(`/terrain/${id}`)
  }

  // 上传地形数据
  async uploadTerrain(file: File, name: string) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('name', name)
    
    return this.post('/terrain/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // 删除地形数据
  async deleteTerrain(id: string) {
    return this.delete(`/terrain/${id}`)
  }
}

// 钻孔数据API
export class DrillHoleAPI extends BaseAPI {
  async getDrillHoleList() {
    return this.get('/drillhole')
  }

  async getDrillHoleById(id: string) {
    return this.get(`/drillhole/${id}`)
  }

  async createDrillHole(data: any) {
    return this.post('/drillhole', data)
  }

  async updateDrillHole(id: string, data: any) {
    return this.put(`/drillhole/${id}`, data)
  }

  async deleteDrillHole(id: string) {
    return this.delete(`/drillhole/${id}`)
  }

  async importDrillHoles(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    
    return this.post('/drillhole/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 地质数据API
export class GeologyAPI extends BaseAPI {
  async getGeologyList() {
    return this.get('/geology')
  }

  async getGeologyById(id: string) {
    return this.get(`/geology/${id}`)
  }

  async createGeology(data: any) {
    return this.post('/geology', data)
  }

  async updateGeology(id: string, data: any) {
    return this.put(`/geology/${id}`, data)
  }

  async deleteGeology(id: string) {
    return this.delete(`/geology/${id}`)
  }
}

// 道路设计API
export class RoadAPI extends BaseAPI {
  async getRoadList() {
    return this.get('/road')
  }

  async getRoadById(id: string) {
    return this.get(`/road/${id}`)
  }

  async createRoad(data: any) {
    return this.post('/road', data)
  }

  async updateRoad(id: string, data: any) {
    return this.put(`/road/${id}`, data)
  }

  async deleteRoad(id: string) {
    return this.delete(`/road/${id}`)
  }

  // 道路选线
  async generateRoadAlignment(startPoint: any, endPoint: any, constraints: any) {
    return this.post('/road/alignment', { startPoint, endPoint, constraints })
  }

  // 道路冲突检测
  async checkRoadConflicts(roadId: string) {
    return this.post(`/road/${roadId}/conflicts`)
  }

  // 道路剖切
  async generateRoadProfile(roadId: string, stations: number[]) {
    return this.post(`/road/${roadId}/profile`, { stations })
  }

  // 安全检测
  async performSafetyCheck(roadId: string) {
    return this.post(`/road/${roadId}/safety-check`)
  }
}

// 运输路线API
export class TransportAPI extends BaseAPI {
  async getRouteList() {
    return this.get('/transport/routes')
  }

  async optimizeRoute(startPoint: any, endPoint: any, constraints: any) {
    return this.post('/transport/optimize', { startPoint, endPoint, constraints })
  }

  async calculateRouteMetrics(routeId: string) {
    return this.get(`/transport/routes/${routeId}/metrics`)
  }
}

// CAD数据API
export class CADAPI extends BaseAPI {
  async getCADList() {
    return this.get('/cad')
  }

  async getCADById(id: string) {
    return this.get(`/cad/${id}`)
  }

  async uploadCAD(file: File, name: string) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('name', name)
    
    return this.post('/cad/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  async deleteCAD(id: string) {
    return this.delete(`/cad/${id}`)
  }

  async exportCAD(id: string, format: string) {
    return this.get(`/cad/${id}/export?format=${format}`, {
      responseType: 'blob'
    })
  }
}

// 导出API实例
export const terrainAPI = new TerrainAPI()
export const drillHoleAPI = new DrillHoleAPI()
export const geologyAPI = new GeologyAPI()
export const roadAPI = new RoadAPI()
export const transportAPI = new TransportAPI()
export const cadAPI = new CADAPI()

export default api
