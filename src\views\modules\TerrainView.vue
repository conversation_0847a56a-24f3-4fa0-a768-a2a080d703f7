<template>
  <div class="terrain-view">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showUploadDialog = true">
        <el-icon><Upload /></el-icon>
        导入地形
      </el-button>
      <el-button @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 地形数据列表 -->
    <div class="data-list">
      <el-scrollbar>
        <div v-if="loading" class="loading-container">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <p>加载中...</p>
        </div>
        
        <div v-else-if="terrainList.length === 0" class="empty-container">
          <el-icon class="empty-icon"><FolderOpened /></el-icon>
          <p>暂无地形数据</p>
          <el-button type="primary" @click="showUploadDialog = true">
            导入地形数据
          </el-button>
        </div>
        
        <div v-else class="terrain-items">
          <div 
            v-for="terrain in terrainList" 
            :key="terrain.id"
            class="terrain-item"
            :class="{ active: currentTerrain?.id === terrain.id }"
            @click="selectTerrain(terrain)"
          >
            <div class="item-header">
              <el-icon class="item-icon"><MapLocation /></el-icon>
              <span class="item-name">{{ terrain.name }}</span>
              <el-dropdown @command="handleCommand">
                <el-button text>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ action: 'view', terrain }">
                      查看详情
                    </el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'export', terrain }">
                      导出数据
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="{ action: 'delete', terrain }"
                      divided
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
            <div class="item-info">
              <div class="info-row">
                <span class="label">类型:</span>
                <span class="value">{{ terrain.type }}</span>
              </div>
              <div class="info-row">
                <span class="label">范围:</span>
                <span class="value">
                  {{ terrain.bounds.minX.toFixed(2) }}, {{ terrain.bounds.minY.toFixed(2) }} - 
                  {{ terrain.bounds.maxX.toFixed(2) }}, {{ terrain.bounds.maxY.toFixed(2) }}
                </span>
              </div>
              <div class="info-row">
                <span class="label">更新时间:</span>
                <span class="value">{{ formatDate(terrain.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="导入地形数据"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="uploadForm" label-width="80px">
        <el-form-item label="数据名称">
          <el-input v-model="uploadForm.name" placeholder="请输入地形数据名称" />
        </el-form-item>
        <el-form-item label="数据类型">
          <el-select v-model="uploadForm.type" placeholder="请选择数据类型">
            <el-option label="DEM数字高程模型" value="DEM" />
            <el-option label="TIN三角网" value="TIN" />
            <el-option label="等高线" value="CONTOUR" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".tif,.tiff,.dem,.xyz,.asc,.las,.laz"
            @change="handleFileChange"
          >
            <el-button>
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="upload-tip">
                支持格式: TIF, DEM, XYZ, ASC, LAS, LAZ
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleUpload"
          :loading="uploading"
          :disabled="!uploadForm.file"
        >
          {{ uploading ? '上传中...' : '确定上传' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTerrainStore } from '@/stores'
import { DateUtils } from '@/utils'
import type { TerrainData } from '@/types'
import {
  Upload,
  Refresh,
  Loading,
  FolderOpened,
  MapLocation,
  MoreFilled
} from '@element-plus/icons-vue'

const terrainStore = useTerrainStore()

const showUploadDialog = ref(false)
const uploading = ref(false)
const uploadRef = ref()

const uploadForm = ref({
  name: '',
  type: '',
  file: null as File | null
})

const loading = computed(() => terrainStore.loading)
const terrainList = computed(() => terrainStore.sortedTerrainList)
const currentTerrain = computed(() => terrainStore.currentTerrain)

onMounted(() => {
  refreshData()
})

const refreshData = async () => {
  try {
    await terrainStore.fetchTerrainList()
  } catch (error) {
    ElMessage.error('获取地形数据失败')
  }
}

const selectTerrain = (terrain: TerrainData) => {
  terrainStore.setCurrentTerrain(terrain)
  // 在3D视图中显示地形
  // TODO: 集成Cesium显示逻辑
}

const handleFileChange = (file: any) => {
  uploadForm.value.file = file.raw
  if (!uploadForm.value.name) {
    uploadForm.value.name = file.name.split('.')[0]
  }
}

const handleUpload = async () => {
  if (!uploadForm.value.file || !uploadForm.value.name || !uploadForm.value.type) {
    ElMessage.warning('请填写完整信息')
    return
  }

  uploading.value = true
  
  try {
    await terrainStore.uploadTerrain(uploadForm.value.file, uploadForm.value.name)
    ElMessage.success('地形数据上传成功')
    showUploadDialog.value = false
    resetUploadForm()
  } catch (error: any) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    uploading.value = false
  }
}

const handleCommand = async (command: any) => {
  const { action, terrain } = command
  
  switch (action) {
    case 'view':
      // 查看详情
      ElMessage.info('查看详情功能开发中')
      break
      
    case 'export':
      // 导出数据
      ElMessage.info('导出功能开发中')
      break
      
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确定要删除地形数据 "${terrain.name}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await terrainStore.deleteTerrain(terrain.id)
        ElMessage.success('删除成功')
      } catch (error: any) {
        if (error !== 'cancel') {
          ElMessage.error(error.message || '删除失败')
        }
      }
      break
  }
}

const resetUploadForm = () => {
  uploadForm.value = {
    name: '',
    type: '',
    file: null
  }
  uploadRef.value?.clearFiles()
}

const formatDate = (date: Date) => {
  return DateUtils.formatDate(new Date(date), 'YYYY-MM-DD HH:mm')
}
</script>

<style scoped>
.terrain-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 16px;
  border-bottom: 1px solid #34495e;
  display: flex;
  gap: 8px;
}

.data-list {
  flex: 1;
  overflow: hidden;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #95a5a6;
}

.loading-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.loading-icon {
  animation: rotating 2s linear infinite;
}

.terrain-items {
  padding: 16px;
}

.terrain-item {
  background: rgba(52, 73, 94, 0.3);
  border: 1px solid #34495e;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.terrain-item:hover {
  background: rgba(52, 73, 94, 0.5);
  border-color: #5a6c7d;
}

.terrain-item.active {
  background: rgba(241, 196, 15, 0.1);
  border-color: #f1c40f;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.item-icon {
  color: #f1c40f;
  margin-right: 8px;
  font-size: 18px;
}

.item-name {
  flex: 1;
  color: #ecf0f1;
  font-weight: 600;
  font-size: 14px;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  font-size: 12px;
}

.info-row .label {
  color: #95a5a6;
  width: 60px;
  flex-shrink: 0;
}

.info-row .value {
  color: #bdc3c7;
  flex: 1;
}

.upload-tip {
  color: #95a5a6;
  font-size: 12px;
  margin-top: 4px;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
